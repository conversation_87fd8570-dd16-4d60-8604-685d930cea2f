<%@ Page Title="" Language="C#" CodeBehind="ToPay.aspx.cs" Inherits="Account.Web.ToPay" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="apple-mobile-web-app-capable" content="no"/>
<meta name="format-detection" content="telephone=no,email=no"/>
<meta name="renderer" content="webkit"/>
<meta http-equiv="Cache-control" content="no-cache">
<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
<title>扫码支付<%=PageTitleConst.Default_Ext %></title>
<style>
/* 现代化支付页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

body {
    background: #f5f7fa;
    min-height: 100vh;
    line-height: 1.5;
    color: #333;
    padding: 8px;
    margin: 0;
}

.body {
    max-width: 380px;
    width: 100%;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    min-height: auto;
}

/* 支付方式选择区域 */
.mod-title {
    background: #fff;
    padding: 16px 20px 8px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
}

.mod-title h1 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

/* 主内容区域 */
.mod-ct {
    background: #fff;
    text-align: center;
    color: #333;
    position: relative;
}

.mod-ct .order {
    font-size: 16px;
    font-weight: 600;
    padding: 12px 16px 8px;
    color: #333;
}

.mod-ct .amount {
    font-size: 32px;
    font-weight: 700;
    margin: 8px 0 16px;
    color: #1890ff;
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 订单详情 */
.mod-ct .detail {
    margin-top: 12px;
    padding: 0 16px 16px;
}

.mod-ct .detail .detail-ct {
    display: none;
    font-size: 12px;
    text-align: left;
    line-height: 20px;
    padding: 12px 16px;
    background: #f8f9fa;
    margin: 0;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.mod-ct .detail .detail-ct dt {
    float: left;
    color: #666;
    min-width: 70px;
    font-weight: 500;
}

.mod-ct .detail .detail-ct dd {
    margin-left: 70px;
    color: #333;
    font-weight: 600;
}

.mod-ct .detail-open {
    border-top: none;
}

.mod-ct .detail .arrow {
    padding: 8px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin: 8px 0;
    cursor: pointer;
    background: #f8f9fa;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 12px;
}

.mod-ct .detail .arrow:hover {
    background: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
}

.mod-ct .detail .arrow .ico-arrow {
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url("./static/image/pay/wechat-pay.png") -25px -100px no-repeat;
    transition: transform 0.2s ease;
}

.mod-ct .detail-open .arrow .ico-arrow {
    background-position: 0 -100px;
    transform: rotate(180deg);
}

.mod-ct .detail-open .detail-ct {
    display: block;
}

/* 支付方式选择器 */
.pay_ul2 {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin: 8px 0 16px;
    padding: 0 16px;
}

.pay_ul2 li {
    border-radius: 8px;
    width: 100px;
    height: 48px;
    border: 1px solid #e0e0e0;
    position: relative;
    text-align: center;
    cursor: pointer;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pay_ul2 li:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.pay_ul2 li img {
    width: 20px;
    height: 20px;
    object-fit: contain;
    margin-bottom: 2px;
}

.pay_ul2 li .pay-method-name {
    font-size: 11px;
    color: #666;
    font-weight: 500;
}

.pay_clickli {
    border: 1px solid #1890ff !important;
    background: #f0f8ff !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2) !important;
}

.pay_clickli .pay-method-name {
    color: #1890ff;
    font-weight: 600;
}

.pay_down_ico {
    display: none;
    width: 16px;
    height: 16px;
    position: absolute;
    bottom: -1px;
    right: -1px;
    background: #1890ff;
    border-radius: 50%;
}

.pay_down_ico::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.pay_clickli .pay_down_ico {
    display: block;
}

/* 订单头部区域 */
.order-header {
    padding: 24px 20px;
    background: #fff;
    border-bottom: 1px solid #f1f3f4;
}

.order-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    text-align: center;
}

.order-amount {
    font-size: 48px;
    font-weight: 700;
    color: #667eea;
    text-align: center;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 超时提示区域 */
.timeout-section {
    padding: 20px;
    text-align: center;
}

.customer-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.customer-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 12px;
    border: 2px solid #e9ecef;
}

.timeout-message {
    font-size: 16px;
    color: #6c757d;
}

.timeout-tip {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border: 1px solid #feb2b2;
    border-radius: 12px;
    padding: 20px;
    margin-top: 16px;
}

.timeout-content {
    text-align: center;
}

.timeout-text {
    display: block;
    font-size: 18px;
    color: #e53e3e;
    font-weight: 600;
    margin-bottom: 12px;
}

.timeout-actions {
    font-size: 16px;
    color: #6c757d;
}

.contact-service {
    color: #667eea;
    font-weight: 600;
    text-decoration: none;
    padding: 6px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.contact-service:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

/* 二维码区域 */
.qrcode-section {
    padding: 16px;
    background: #fafafa;
}

.qrcode-container {
    max-width: 200px;
    margin: 0 auto;
}

.qrcode-wrapper {
    position: relative;
    display: inline-block;
    padding: 12px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 12px;
    border: 1px solid #e0e0e0;
}

.qr-image {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 160px;
    background: white;
    border-radius: 4px;
    position: relative;
}

.qr-image canvas,
.qr-image img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.qrcode-tip {
    text-align: center;
}

.scan-instruction {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.scan-instruction b {
    color: #1890ff;
}

.scan-description {
    font-size: 12px;
    color: #666;
    margin: 0;
}

/* 倒计时区域 */
.countdown-section {
    padding: 12px 16px;
    text-align: center;
    background: #fff8e6;
    border-top: 1px solid #f0f0f0;
}

.countdown-title {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.time-item strong {
    background: #52c41a;
    color: #fff;
    line-height: 24px;
    font-size: 12px;
    font-weight: 600;
    padding: 0 6px;
    margin-right: 4px;
    border-radius: 4px;
    display: inline-block;
    min-width: 32px;
    text-align: center;
}

/* 提示信息区域 */
.payment-tips {
    padding: 12px 16px;
    background: #f6ffed;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border-left: 3px solid #1890ff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tip-item:last-child {
    margin-bottom: 0;
}

.tip-icon {
    font-size: 14px;
    margin-right: 8px;
    flex-shrink: 0;
    margin-top: 1px;
}

.tip-text {
    font-size: 12px;
    line-height: 1.4;
    color: #333;
}

.help-text {
    color: #666;
}

/* 订单详情区域 */
.order-details {
    padding: 0 20px 24px;
}

.arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin: 16px 0;
    cursor: pointer;
    background: #f8f9fa;
    text-decoration: none;
    color: #6c757d;
    font-size: 14px;
    transition: all 0.3s ease;
}

.arrow:hover {
    background: #e9ecef;
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-1px);
}

.arrow-text {
    font-weight: 500;
}

.status-waiting {
    color: #fa8c16;
    font-weight: 600;
    padding: 4px 8px;
    background: #fff7e6;
    border-radius: 6px;
    font-size: 12px;
}

/* 警告状态样式 */
.countdown-section.warning {
    background: linear-gradient(135deg, #fff2f0 0%, #fed7d7 100%);
    border-color: #fa8c16;
}

.warning-blink {
    animation: warningBlink 1s infinite;
}

@keyframes warningBlink {
    0%, 50% {
        background: #fa8c16;
        transform: scale(1);
    }
    25%, 75% {
        background: #ff7875;
        transform: scale(1.05);
    }
}

/* 消息层样式 */
.message-layer {
    pointer-events: none;
}

.message-layer .custom-msg-content {
    pointer-events: auto;
}

/* 响应式设计 */
@media (max-width: 400px) {
    body {
        padding: 4px;
    }

    .body {
        max-width: 100%;
        border-radius: 8px;
    }

    .pay_ul2 {
        gap: 8px;
        padding: 0 12px;
    }

    .pay_ul2 li {
        width: 90px;
        height: 44px;
    }

    .mod-ct .amount {
        font-size: 28px;
    }

    .qrcode-container {
        max-width: 180px;
    }

    .qr-image {
        min-height: 140px;
    }

    .message-layer {
        left: 8px !important;
        right: 8px;
        max-width: none;
    }
}

/* 确保页面紧凑 */
.mod-ct .qr-image {
    margin: 8px 0 !important;
}

.countdown-section {
    border-bottom: none;
}

.payment-tips {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

/* 二维码加载和错误状态 */
.qrcode-loading,
.qrcode-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 40px 20px;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text,
.error-text {
    font-size: 14px;
    color: #6c757d;
    margin-top: 8px;
    text-align: center;
}

.error-icon {
    font-size: 32px;
    color: #e53e3e;
}

.retry-btn {
    margin-top: 12px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}
</style>
</head>
<body>
<div class="body" id="body">
<div class="mod-title" style="display:none;">
    <h1 style="margin-bottom:8px;color:#333;">选择支付方式</h1>
    <ul class="pay_ul2">
        <li id="alipayLi" class="pay_clickli" onclick="changePay(0)">
            <img class="pay_fs" src="./static/image/pay/alipay.jpg" title="支付宝" alt="支付宝">
            <div class="pay-method-name">支付宝</div>
            <div class="pay_down_ico"></div>
        </li>
        <li id="wxpayLi" onclick="changePay(1)">
            <img class="pay_fs" src="./static/image/pay/weixin.jpg" title="微信支付" alt="微信支付">
            <div class="pay-method-name">微信支付</div>
            <div class="pay_down_ico"></div>
        </li>
    </ul>
</div>
<div class="mod-ct" id="loadingDiv"><img id="loading" src="./static/image/pay/loading.gif" style="width:150px;height:150px"></div>
<div class="mod-ct" id="orderDiv" style="display:none">
    <div class="order-header">
        <div class="order-title" id="strRemark"></div>
        <div class="order-amount" id="money"></div>
        <div class="timeout-section" id="timeOut" style="display:none;">
            <div class="customer-info">
                <img id="qqHead" class="customer-avatar" style="display:none" alt="客服头像">
                <span class="timeout-message" id="strRemark"></span>
            </div>
            <div class="timeout-tip" id="timeOutTip" style="display:none">
                <div class="timeout-content">
                    <span class="timeout-text">支付超时，订单已关闭！</span>
                    <div class="timeout-actions">
                        <span>请重新下单或</span>
                        <a id="keFuQQ1" href="javascript:void(0)" target="_blank" class="contact-service">联系客服</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="orderbody">
        <div class="qrcode-section">
            <div class="qrcode-container">
                <div class="qrcode-wrapper">
                    <div class="qr-image" id="qrcode"></div>
                </div>
                <div class="qrcode-tip">
                    <div class="tip-text">
                        <p class="scan-instruction">使用 <b id="payTypeMsg">支付宝</b> 扫一扫</p>
                        <p class="scan-description">扫描二维码完成支付</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="countdown-section">
            <div class="countdown-title">支付剩余时间</div>
            <div class="time-item">
                <strong id="hour_show" style="display:none;">0时</strong>
                <strong id="minute_show">0分</strong>
                <strong id="second_show">0秒</strong>
            </div>
        </div>
        <div class="payment-tips" id="msg">
            <div class="tip-item" id="lblYouHui" style="display:none;">
                <span class="tip-icon">⚠️</span>
                <span class="tip-text">请务必与订单金额一致，以免订单失败！</span>
            </div>
            <div class="tip-item">
                <span class="tip-icon">💡</span>
                <span class="tip-text">
                    支付完成后，请耐心等待页面跳转
                    <br>
                    <span class="help-text">有问题请</span>
                    <a id="keFuQQ" href="javascript:void(0)" target="_blank" class="contact-service">联系客服</a>
                </span>
            </div>
        </div>
        <div class="order-details">
            <div class="detail" id="orderDetail">
                <dl class="detail-ct" id="desc" style="display:none">
                    <dt>支付金额</dt>
                    <dd>￥<b id="strPrice"></b></dd>
                    <dt>订单编号</dt>
                    <dd><b id="strOrderId"></b></dd>
                    <dt>商户订单号</dt>
                    <dd><b id="strPayId"></b></dd>
                    <dt>创建时间</dt>
                    <dd><b id="strDate"></b></dd>
                    <dt>支付状态</dt>
                    <dd><span class="status-waiting">等待支付</span></dd>
                </dl>
                <a href="javascript:void(0)" class="arrow" onclick="showDetail()">
                    <span class="arrow-text">查看订单详情</span>
                    <i class="ico-arrow"></i>
                </a>
            </div>
        </div>
    </div>
</div>
</div>
    <script>
        var qrcode=function(){var t=function(t,r){var e=t,n=g[r],o=null,i=0,a=null,u=[],f={},c=function(t,r){o=function(t){for(var r=new Array(t),e=0;e<t;e+=1){r[e]=new Array(t);for(var n=0;n<t;n+=1)r[e][n]=null}return r}(i=4*e+17),l(0,0),l(i-7,0),l(0,i-7),s(),h(),d(t,r),e>=7&&v(t),null==a&&(a=p(e,n,u)),w(a,r)},l=function(t,r){for(var e=-1;e<=7;e+=1)if(!(t+e<=-1||i<=t+e))for(var n=-1;n<=7;n+=1)r+n<=-1||i<=r+n||(o[t+e][r+n]=0<=e&&e<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==e||6==e)||2<=e&&e<=4&&2<=n&&n<=4)},h=function(){for(var t=8;t<i-8;t+=1)null==o[t][6]&&(o[t][6]=t%2==0);for(var r=8;r<i-8;r+=1)null==o[6][r]&&(o[6][r]=r%2==0)},s=function(){for(var t=B.getPatternPosition(e),r=0;r<t.length;r+=1)for(var n=0;n<t.length;n+=1){var i=t[r],a=t[n];if(null==o[i][a])for(var u=-2;u<=2;u+=1)for(var f=-2;f<=2;f+=1)o[i+u][a+f]=-2==u||2==u||-2==f||2==f||0==u&&0==f}},v=function(t){for(var r=B.getBCHTypeNumber(e),n=0;n<18;n+=1){var a=!t&&1==(r>>n&1);o[Math.floor(n/3)][n%3+i-8-3]=a}for(n=0;n<18;n+=1){a=!t&&1==(r>>n&1);o[n%3+i-8-3][Math.floor(n/3)]=a}},d=function(t,r){for(var e=n<<3|r,a=B.getBCHTypeInfo(e),u=0;u<15;u+=1){var f=!t&&1==(a>>u&1);u<6?o[u][8]=f:u<8?o[u+1][8]=f:o[i-15+u][8]=f}for(u=0;u<15;u+=1){f=!t&&1==(a>>u&1);u<8?o[8][i-u-1]=f:u<9?o[8][15-u-1+1]=f:o[8][15-u-1]=f}o[i-8][8]=!t},w=function(t,r){for(var e=-1,n=i-1,a=7,u=0,f=B.getMaskFunction(r),c=i-1;c>0;c-=2)for(6==c&&(c-=1);;){for(var g=0;g<2;g+=1)if(null==o[n][c-g]){var l=!1;u<t.length&&(l=1==(t[u]>>>a&1)),f(n,c-g)&&(l=!l),o[n][c-g]=l,-1==(a-=1)&&(u+=1,a=7)}if((n+=e)<0||i<=n){n-=e,e=-e;break}}},p=function(t,r,e){for(var n=A.getRSBlocks(t,r),o=b(),i=0;i<e.length;i+=1){var a=e[i];o.put(a.getMode(),4),o.put(a.getLength(),B.getLengthInBits(a.getMode(),t)),a.write(o)}var u=0;for(i=0;i<n.length;i+=1)u+=n[i].dataCount;if(o.getLengthInBits()>8*u)throw"code length overflow. ("+o.getLengthInBits()+">"+8*u+")";for(o.getLengthInBits()+4<=8*u&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(!1);for(;!(o.getLengthInBits()>=8*u||(o.put(236,8),o.getLengthInBits()>=8*u));)o.put(17,8);return function(t,r){for(var e=0,n=0,o=0,i=new Array(r.length),a=new Array(r.length),u=0;u<r.length;u+=1){var f=r[u].dataCount,c=r[u].totalCount-f;n=Math.max(n,f),o=Math.max(o,c),i[u]=new Array(f);for(var g=0;g<i[u].length;g+=1)i[u][g]=255&t.getBuffer()[g+e];e+=f;var l=B.getErrorCorrectPolynomial(c),h=k(i[u],l.getLength()-1).mod(l);for(a[u]=new Array(l.getLength()-1),g=0;g<a[u].length;g+=1){var s=g+h.getLength()-a[u].length;a[u][g]=s>=0?h.getAt(s):0}}var v=0;for(g=0;g<r.length;g+=1)v+=r[g].totalCount;var d=new Array(v),w=0;for(g=0;g<n;g+=1)for(u=0;u<r.length;u+=1)g<i[u].length&&(d[w]=i[u][g],w+=1);for(g=0;g<o;g+=1)for(u=0;u<r.length;u+=1)g<a[u].length&&(d[w]=a[u][g],w+=1);return d}(o,n)};f.addData=function(t,r){var e=null;switch(r=r||"Byte"){case"Numeric":e=M(t);break;case"Alphanumeric":e=x(t);break;case"Byte":e=m(t);break;case"Kanji":e=L(t);break;default:throw"mode:"+r}u.push(e),a=null},f.isDark=function(t,r){if(t<0||i<=t||r<0||i<=r)throw t+","+r;return o[t][r]},f.getModuleCount=function(){return i},f.make=function(){if(e<1){for(var t=1;t<40;t++){for(var r=A.getRSBlocks(t,n),o=b(),i=0;i<u.length;i++){var a=u[i];o.put(a.getMode(),4),o.put(a.getLength(),B.getLengthInBits(a.getMode(),t)),a.write(o)}var g=0;for(i=0;i<r.length;i++)g+=r[i].dataCount;if(o.getLengthInBits()<=8*g)break}e=t}c(!1,function(){for(var t=0,r=0,e=0;e<8;e+=1){c(!0,e);var n=B.getLostPoint(f);(0==e||t>n)&&(t=n,r=e)}return r}())},f.createTableTag=function(t,r){t=t||2;var e="";e+='<table style="',e+=" border-width: 0px; border-style: none;",e+=" border-collapse: collapse;",e+=" padding: 0px; margin: "+(r=void 0===r?4*t:r)+"px;",e+='">',e+="<tbody>";for(var n=0;n<f.getModuleCount();n+=1){e+="<tr>";for(var o=0;o<f.getModuleCount();o+=1)e+='<td style="',e+=" border-width: 0px; border-style: none;",e+=" border-collapse: collapse;",e+=" padding: 0px; margin: 0px;",e+=" width: "+t+"px;",e+=" height: "+t+"px;",e+=" background-color: ",e+=f.isDark(n,o)?"#000000":"#ffffff",e+=";",e+='"/>';e+="</tr>"}return e+="</tbody>",e+="</table>"},f.createSvgTag=function(t,r,e,n){var o={};"object"==typeof arguments[0]&&(t=(o=arguments[0]).cellSize,r=o.margin,e=o.alt,n=o.title),t=t||2,r=void 0===r?4*t:r,(e="string"==typeof e?{text:e}:e||{}).text=e.text||null,e.id=e.text?e.id||"qrcode-description":null,(n="string"==typeof n?{text:n}:n||{}).text=n.text||null,n.id=n.text?n.id||"qrcode-title":null;var i,a,u,c,g=f.getModuleCount()*t+2*r,l="";for(c="l"+t+",0 0,"+t+" -"+t+",0 0,-"+t+"z ",l+='<svg version="1.1" xmlns="http://www.w3.org/2000/svg"',l+=o.scalable?"":' width="'+g+'px" height="'+g+'px"',l+=' viewBox="0 0 '+g+" "+g+'" ',l+=' preserveAspectRatio="xMinYMin meet"',l+=n.text||e.text?' role="img" aria-labelledby="'+y([n.id,e.id].join(" ").trim())+'"':"",l+=">",l+=n.text?'<title id="'+y(n.id)+'">'+y(n.text)+"</title>":"",l+=e.text?'<description id="'+y(e.id)+'">'+y(e.text)+"</description>":"",l+='<rect width="100%" height="100%" fill="white" cx="0" cy="0"/>',l+='<path d="',a=0;a<f.getModuleCount();a+=1)for(u=a*t+r,i=0;i<f.getModuleCount();i+=1)f.isDark(a,i)&&(l+="M"+(i*t+r)+","+u+c);return l+='" stroke="transparent" fill="black"/>',l+="</svg>"},f.createDataURL=function(t,r){t=t||2,r=void 0===r?4*t:r;var e=f.getModuleCount()*t+2*r,n=r,o=e-r;return I(e,e,(function(r,e){if(n<=r&&r<o&&n<=e&&e<o){var i=Math.floor((r-n)/t),a=Math.floor((e-n)/t);return f.isDark(a,i)?0:1}return 1}))},f.createImgTag=function(t,r,e){t=t||2,r=void 0===r?4*t:r;var n=f.getModuleCount()*t+2*r,o="";return o+="<img",o+=' src="',o+=f.createDataURL(t,r),o+='"',o+=' width="',o+=n,o+='"',o+=' height="',o+=n,o+='"',e&&(o+=' alt="',o+=y(e),o+='"'),o+="/>"};var y=function(t){for(var r="",e=0;e<t.length;e+=1){var n=t.charAt(e);switch(n){case"<":r+="&lt;";break;case">":r+="&gt;";break;case"&":r+="&amp;";break;case'"':r+="&quot;";break;default:r+=n}}return r};return f.createASCII=function(t,r){if((t=t||1)<2)return function(t){t=void 0===t?2:t;var r,e,n,o,i,a=1*f.getModuleCount()+2*t,u=t,c=a-t,g={"██":"█","█ ":"▀"," █":"▄","  ":" "},l={"██":"▀","█ ":"▀"," █":" ","  ":" "},h="";for(r=0;r<a;r+=2){for(n=Math.floor((r-u)/1),o=Math.floor((r+1-u)/1),e=0;e<a;e+=1)i="█",u<=e&&e<c&&u<=r&&r<c&&f.isDark(n,Math.floor((e-u)/1))&&(i=" "),u<=e&&e<c&&u<=r+1&&r+1<c&&f.isDark(o,Math.floor((e-u)/1))?i+=" ":i+="█",h+=t<1&&r+1>=c?l[i]:g[i];h+="\n"}return a%2&&t>0?h.substring(0,h.length-a-1)+Array(a+1).join("▀"):h.substring(0,h.length-1)}(r);t-=1,r=void 0===r?2*t:r;var e,n,o,i,a=f.getModuleCount()*t+2*r,u=r,c=a-r,g=Array(t+1).join("██"),l=Array(t+1).join("  "),h="",s="";for(e=0;e<a;e+=1){for(o=Math.floor((e-u)/t),s="",n=0;n<a;n+=1)i=1,u<=n&&n<c&&u<=e&&e<c&&f.isDark(o,Math.floor((n-u)/t))&&(i=0),s+=i?g:l;for(o=0;o<t;o+=1)h+=s+"\n"}return h.substring(0,h.length-1)},f.renderTo2dContext=function(t,r){r=r||2;for(var e=f.getModuleCount(),n=0;n<e;n++)for(var o=0;o<e;o++)t.fillStyle=f.isDark(n,o)?"black":"white",t.fillRect(n*r,o*r,r,r)},f};t.stringToBytes=(t.stringToBytesFuncs={default:function(t){for(var r=[],e=0;e<t.length;e+=1){var n=t.charCodeAt(e);r.push(255&n)}return r}}).default,t.createStringToBytes=function(t,r){var e=function(){for(var e=S(t),n=function(){var t=e.read();if(-1==t)throw"eof";return t},o=0,i={};;){var a=e.read();if(-1==a)break;var u=n(),f=n()<<8|n();i[String.fromCharCode(a<<8|u)]=f,o+=1}if(o!=r)throw o+" != "+r;return i}(),n="?".charCodeAt(0);return function(t){for(var r=[],o=0;o<t.length;o+=1){var i=t.charCodeAt(o);if(i<128)r.push(i);else{var a=e[t.charAt(o)];"number"==typeof a?(255&a)==a?r.push(a):(r.push(a>>>8),r.push(255&a)):r.push(n)}}return r}};var r,e,n,o,i,a=1,u=2,f=4,c=8,g={L:1,M:0,Q:3,H:2},l=0,h=1,s=2,v=3,d=4,w=5,p=6,y=7,B=(r=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],e=1335,n=7973,i=function(t){for(var r=0;0!=t;)r+=1,t>>>=1;return r},(o={}).getBCHTypeInfo=function(t){for(var r=t<<10;i(r)-i(e)>=0;)r^=e<<i(r)-i(e);return 21522^(t<<10|r)},o.getBCHTypeNumber=function(t){for(var r=t<<12;i(r)-i(n)>=0;)r^=n<<i(r)-i(n);return t<<12|r},o.getPatternPosition=function(t){return r[t-1]},o.getMaskFunction=function(t){switch(t){case l:return function(t,r){return(t+r)%2==0};case h:return function(t,r){return t%2==0};case s:return function(t,r){return r%3==0};case v:return function(t,r){return(t+r)%3==0};case d:return function(t,r){return(Math.floor(t/2)+Math.floor(r/3))%2==0};case w:return function(t,r){return t*r%2+t*r%3==0};case p:return function(t,r){return(t*r%2+t*r%3)%2==0};case y:return function(t,r){return(t*r%3+(t+r)%2)%2==0};default:throw"bad maskPattern:"+t}},o.getErrorCorrectPolynomial=function(t){for(var r=k([1],0),e=0;e<t;e+=1)r=r.multiply(k([1,C.gexp(e)],0));return r},o.getLengthInBits=function(t,r){if(1<=r&&r<10)switch(t){case a:return 10;case u:return 9;case f:case c:return 8;default:throw"mode:"+t}else if(r<27)switch(t){case a:return 12;case u:return 11;case f:return 16;case c:return 10;default:throw"mode:"+t}else{if(!(r<41))throw"type:"+r;switch(t){case a:return 14;case u:return 13;case f:return 16;case c:return 12;default:throw"mode:"+t}}},o.getLostPoint=function(t){for(var r=t.getModuleCount(),e=0,n=0;n<r;n+=1)for(var o=0;o<r;o+=1){for(var i=0,a=t.isDark(n,o),u=-1;u<=1;u+=1)if(!(n+u<0||r<=n+u))for(var f=-1;f<=1;f+=1)o+f<0||r<=o+f||0==u&&0==f||a==t.isDark(n+u,o+f)&&(i+=1);i>5&&(e+=3+i-5)}for(n=0;n<r-1;n+=1)for(o=0;o<r-1;o+=1){var c=0;t.isDark(n,o)&&(c+=1),t.isDark(n+1,o)&&(c+=1),t.isDark(n,o+1)&&(c+=1),t.isDark(n+1,o+1)&&(c+=1),0!=c&&4!=c||(e+=3)}for(n=0;n<r;n+=1)for(o=0;o<r-6;o+=1)t.isDark(n,o)&&!t.isDark(n,o+1)&&t.isDark(n,o+2)&&t.isDark(n,o+3)&&t.isDark(n,o+4)&&!t.isDark(n,o+5)&&t.isDark(n,o+6)&&(e+=40);for(o=0;o<r;o+=1)for(n=0;n<r-6;n+=1)t.isDark(n,o)&&!t.isDark(n+1,o)&&t.isDark(n+2,o)&&t.isDark(n+3,o)&&t.isDark(n+4,o)&&!t.isDark(n+5,o)&&t.isDark(n+6,o)&&(e+=40);var g=0;for(o=0;o<r;o+=1)for(n=0;n<r;n+=1)t.isDark(n,o)&&(g+=1);return e+=Math.abs(100*g/r/r-50)/5*10},o),C=function(){for(var t=new Array(256),r=new Array(256),e=0;e<8;e+=1)t[e]=1<<e;for(e=8;e<256;e+=1)t[e]=t[e-4]^t[e-5]^t[e-6]^t[e-8];for(e=0;e<255;e+=1)r[t[e]]=e;var n={glog:function(t){if(t<1)throw"glog("+t+")";return r[t]},gexp:function(r){for(;r<0;)r+=255;for(;r>=256;)r-=255;return t[r]}};return n}();function k(t,r){if(void 0===t.length)throw t.length+"/"+r;var e=function(){for(var e=0;e<t.length&&0==t[e];)e+=1;for(var n=new Array(t.length-e+r),o=0;o<t.length-e;o+=1)n[o]=t[o+e];return n}(),n={getAt:function(t){return e[t]},getLength:function(){return e.length},multiply:function(t){for(var r=new Array(n.getLength()+t.getLength()-1),e=0;e<n.getLength();e+=1)for(var o=0;o<t.getLength();o+=1)r[e+o]^=C.gexp(C.glog(n.getAt(e))+C.glog(t.getAt(o)));return k(r,0)},mod:function(t){if(n.getLength()-t.getLength()<0)return n;for(var r=C.glog(n.getAt(0))-C.glog(t.getAt(0)),e=new Array(n.getLength()),o=0;o<n.getLength();o+=1)e[o]=n.getAt(o);for(o=0;o<t.getLength();o+=1)e[o]^=C.gexp(C.glog(t.getAt(o))+r);return k(e,0).mod(t)}};return n}var A=function(){var t=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],r=function(t,r){var e={};return e.totalCount=t,e.dataCount=r,e},e={};return e.getRSBlocks=function(e,n){var o=function(r,e){switch(e){case g.L:return t[4*(r-1)+0];case g.M:return t[4*(r-1)+1];case g.Q:return t[4*(r-1)+2];case g.H:return t[4*(r-1)+3];default:return}}(e,n);if(void 0===o)throw"bad rs block @ typeNumber:"+e+"/errorCorrectionLevel:"+n;for(var i=o.length/3,a=[],u=0;u<i;u+=1)for(var f=o[3*u+0],c=o[3*u+1],l=o[3*u+2],h=0;h<f;h+=1)a.push(r(c,l));return a},e}(),b=function(){var t=[],r=0,e={getBuffer:function(){return t},getAt:function(r){var e=Math.floor(r/8);return 1==(t[e]>>>7-r%8&1)},put:function(t,r){for(var n=0;n<r;n+=1)e.putBit(1==(t>>>r-n-1&1))},getLengthInBits:function(){return r},putBit:function(e){var n=Math.floor(r/8);t.length<=n&&t.push(0),e&&(t[n]|=128>>>r%8),r+=1}};return e},M=function(t){var r=a,e=t,n={getMode:function(){return r},getLength:function(t){return e.length},write:function(t){for(var r=e,n=0;n+2<r.length;)t.put(o(r.substring(n,n+3)),10),n+=3;n<r.length&&(r.length-n==1?t.put(o(r.substring(n,n+1)),4):r.length-n==2&&t.put(o(r.substring(n,n+2)),7))}},o=function(t){for(var r=0,e=0;e<t.length;e+=1)r=10*r+i(t.charAt(e));return r},i=function(t){if("0"<=t&&t<="9")return t.charCodeAt(0)-"0".charCodeAt(0);throw"illegal char :"+t};return n},x=function(t){var r=u,e=t,n={getMode:function(){return r},getLength:function(t){return e.length},write:function(t){for(var r=e,n=0;n+1<r.length;)t.put(45*o(r.charAt(n))+o(r.charAt(n+1)),11),n+=2;n<r.length&&t.put(o(r.charAt(n)),6)}},o=function(t){if("0"<=t&&t<="9")return t.charCodeAt(0)-"0".charCodeAt(0);if("A"<=t&&t<="Z")return t.charCodeAt(0)-"A".charCodeAt(0)+10;switch(t){case" ":return 36;case"$":return 37;case"%":return 38;case"*":return 39;case"+":return 40;case"-":return 41;case".":return 42;case"/":return 43;case":":return 44;default:throw"illegal char :"+t}};return n},m=function(r){var e=f,n=t.stringToBytes(r),o={getMode:function(){return e},getLength:function(t){return n.length},write:function(t){for(var r=0;r<n.length;r+=1)t.put(n[r],8)}};return o},L=function(r){var e=c,n=t.stringToBytesFuncs.SJIS;if(!n)throw"sjis not supported.";!function(t,r){var e=n("友");if(2!=e.length||38726!=(e[0]<<8|e[1]))throw"sjis not supported."}();var o=n(r),i={getMode:function(){return e},getLength:function(t){return~~(o.length/2)},write:function(t){for(var r=o,e=0;e+1<r.length;){var n=(255&r[e])<<8|255&r[e+1];if(33088<=n&&n<=40956)n-=33088;else{if(!(57408<=n&&n<=60351))throw"illegal char at "+(e+1)+"/"+n;n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13),e+=2}if(e<r.length)throw"illegal char at "+(e+1)}};return i},D=function(){var t=[],r={writeByte:function(r){t.push(255&r)},writeShort:function(t){r.writeByte(t),r.writeByte(t>>>8)},writeBytes:function(t,e,n){e=e||0,n=n||t.length;for(var o=0;o<n;o+=1)r.writeByte(t[o+e])},writeString:function(t){for(var e=0;e<t.length;e+=1)r.writeByte(t.charCodeAt(e))},toByteArray:function(){return t},toString:function(){var r="";r+="[";for(var e=0;e<t.length;e+=1)e>0&&(r+=","),r+=t[e];return r+="]"}};return r},S=function(t){var r=t,e=0,n=0,o=0,i={read:function(){for(;o<8;){if(e>=r.length){if(0==o)return-1;throw"unexpected end of file./"+o}var t=r.charAt(e);if(e+=1,"="==t)return o=0,-1;t.match(/^\s$/)||(n=n<<6|a(t.charCodeAt(0)),o+=6)}var i=n>>>o-8&255;return o-=8,i}},a=function(t){if(65<=t&&t<=90)return t-65;if(97<=t&&t<=122)return t-97+26;if(48<=t&&t<=57)return t-48+52;if(43==t)return 62;if(47==t)return 63;throw"c:"+t};return i},I=function(t,r,e){for(var n=function(t,r){var e=t,n=r,o=new Array(t*r),i={setPixel:function(t,r,n){o[r*e+t]=n},write:function(t){t.writeString("GIF87a"),t.writeShort(e),t.writeShort(n),t.writeByte(128),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(255),t.writeByte(255),t.writeByte(255),t.writeString(","),t.writeShort(0),t.writeShort(0),t.writeShort(e),t.writeShort(n),t.writeByte(0);var r=a(2);t.writeByte(2);for(var o=0;r.length-o>255;)t.writeByte(255),t.writeBytes(r,o,255),o+=255;t.writeByte(r.length-o),t.writeBytes(r,o,r.length-o),t.writeByte(0),t.writeString(";")}},a=function(t){for(var r=1<<t,e=1+(1<<t),n=t+1,i=u(),a=0;a<r;a+=1)i.add(String.fromCharCode(a));i.add(String.fromCharCode(r)),i.add(String.fromCharCode(e));var f,c,g,l=D(),h=(f=l,c=0,g=0,{write:function(t,r){if(t>>>r!=0)throw"length over";for(;c+r>=8;)f.writeByte(255&(t<<c|g)),r-=8-c,t>>>=8-c,g=0,c=0;g|=t<<c,c+=r},flush:function(){c>0&&f.writeByte(g)}});h.write(r,n);var s=0,v=String.fromCharCode(o[s]);for(s+=1;s<o.length;){var d=String.fromCharCode(o[s]);s+=1,i.contains(v+d)?v+=d:(h.write(i.indexOf(v),n),i.size()<4095&&(i.size()==1<<n&&(n+=1),i.add(v+d)),v=d)}return h.write(i.indexOf(v),n),h.write(e,n),h.flush(),l.toByteArray()},u=function(){var t={},r=0,e={add:function(n){if(e.contains(n))throw"dup key:"+n;t[n]=r,r+=1},size:function(){return r},indexOf:function(r){return t[r]},contains:function(r){return void 0!==t[r]}};return e};return i}(t,r),o=0;o<r;o+=1)for(var i=0;i<t;i+=1)n.setPixel(i,o,e(i,o));var a=D();n.write(a);for(var u=function(){var t=0,r=0,e=0,n="",o={},i=function(t){n+=String.fromCharCode(a(63&t))},a=function(t){if(t<0);else{if(t<26)return 65+t;if(t<52)return t-26+97;if(t<62)return t-52+48;if(62==t)return 43;if(63==t)return 47}throw"n:"+t};return o.writeByte=function(n){for(t=t<<8|255&n,r+=8,e+=1;r>=6;)i(t>>>r-6),r-=6},o.flush=function(){if(r>0&&(i(t<<6-r),t=0,r=0),e%3!=0)for(var o=3-e%3,a=0;a<o;a+=1)n+="="},o.toString=function(){return n},o}(),f=a.toByteArray(),c=0;c<f.length;c+=1)u.writeByte(f[c]);return u.flush(),"data:image/gif;base64,"+u};return t}();qrcode.stringToBytesFuncs["UTF-8"]=function(t){return function(t){for(var r=[],e=0;e<t.length;e++){var n=t.charCodeAt(e);n<128?r.push(n):n<2048?r.push(192|n>>6,128|63&n):n<55296||n>=57344?r.push(224|n>>12,128|n>>6&63,128|63&n):(e++,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),r.push(240|n>>18,128|n>>12&63,128|n>>6&63,128|63&n))}return r}(t)};
    </script>
<script>
// 配置常量
var CONFIG = {
    QRCODE_SIZE: 160,
    CHECK_INTERVAL: 1800,
    TICK_INTERVAL: 1000,
    MSG_TIMEOUT: 3000,
    ANIMATION_DURATION: 200,
    COLORS: {
        SUCCESS: '#52c41a',
        ERROR: '#f5222d',
        WARNING: '#fa8c16',
        PRIMARY: '#1890ff'
    },
    BREAKPOINTS: {
        MOBILE: 400,
        TABLET: 768
    }
};

// DOM元素缓存和操作工具
var Elements = {
    cache: {},
    get: function(selector) {
        if (!this.cache[selector]) {
            this.cache[selector] = typeof selector === 'string'
                ? document.querySelector(selector)
                : selector;
        }
        return this.cache[selector];
    },
    getById: function(id) {
        var key = '#' + id;
        if (!this.cache[key]) {
            this.cache[key] = document.getElementById(id);
        }
        return this.cache[key];
    },
    clearCache: function() {
        this.cache = {};
    }
};

// 简化的选择器函数
var $ = function(selector) {
    return Elements.get(selector);
};

// DOM操作工具集
var DOMUtils = {
    addClass: function(element, className) {
        if (element && element.classList) {
            element.classList.add(className);
        }
    },

    removeClass: function(element, className) {
        if (element && element.classList) {
            element.classList.remove(className);
        }
    },

    hasClass: function(element, className) {
        return element && element.classList && element.classList.contains(className);
    },

    toggleClass: function(element, className) {
        if (element && element.classList) {
            element.classList.toggle(className);
        }
    },

    show: function(element, display) {
        if (element) {
            element.style.display = display || '';
        }
    },

    hide: function(element) {
        if (element) {
            element.style.display = 'none';
        }
    },

    html: function(element, content) {
        if (!element) return null;
        if (content !== undefined) {
            element.innerHTML = content;
            return element;
        }
        return element.innerHTML;
    },

    text: function(element, content) {
        if (!element) return null;
        if (content !== undefined) {
            element.textContent = content;
            return element;
        }
        return element.textContent;
    },

    css: function(element, property, value) {
        if (!element || !element.style) return null;

        if (typeof property === 'object') {
            // 批量设置样式
            for (var key in property) {
                if (property.hasOwnProperty(key)) {
                    element.style[key] = property[key];
                }
            }
            return element;
        }

        if (value !== undefined) {
            element.style[property] = value;
            return element;
        }

        return getComputedStyle(element)[property];
    },

    attr: function(element, name, value) {
        if (!element) return null;
        if (value !== undefined) {
            element.setAttribute(name, value);
            return element;
        }
        return element.getAttribute(name);
    },

    fadeIn: function(element, duration) {
        if (!element) return;
        duration = duration || CONFIG.ANIMATION_DURATION;

        element.style.opacity = '0';
        element.style.display = '';
        element.style.transition = 'opacity ' + duration + 'ms ease';

        setTimeout(function() {
            element.style.opacity = '1';
        }, 10);
    },

    fadeOut: function(element, duration, callback) {
        if (!element) return;
        duration = duration || CONFIG.ANIMATION_DURATION;

        element.style.transition = 'opacity ' + duration + 'ms ease';
        element.style.opacity = '0';

        setTimeout(function() {
            element.style.display = 'none';
            if (callback) callback();
        }, duration);
    }
};

// AJAX请求工具
var AjaxUtils = {
    post: function(url, data, callback, errorCallback) {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', url, true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (callback) callback(response);
                    } catch (e) {
                        console.error('JSON解析错误:', e);
                        if (errorCallback) errorCallback('数据解析失败');
                    }
                } else {
                    console.error('请求失败:', xhr.status);
                    if (errorCallback) errorCallback('网络请求失败');
                }
            }
        };

        xhr.onerror = function() {
            console.error('网络错误');
            if (errorCallback) errorCallback('网络连接失败');
        };

        xhr.send(data);
    }
};

// 二维码生成工具
var QRCode = {
    generate: function(text, options) {
        options = options || {};
        var size = options.width || options.height || CONFIG.QRCODE_SIZE;
        var canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;

        var ctx = canvas.getContext('2d');

        try {
            var qr = qrcode(0, 'M');
            qr.addData(text);
            qr.make();

            var moduleCount = qr.getModuleCount();
            var cellSize = size / moduleCount;

            // 绘制背景
            ctx.fillStyle = options.background || '#ffffff';
            ctx.fillRect(0, 0, size, size);

            // 绘制二维码
            ctx.fillStyle = options.foreground || '#000000';
            for (var row = 0; row < moduleCount; row++) {
                for (var col = 0; col < moduleCount; col++) {
                    if (qr.isDark(row, col)) {
                        ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                    }
                }
            }

            // 添加圆角效果
            if (options.borderRadius) {
                this.addBorderRadius(canvas, options.borderRadius);
            }

        } catch (error) {
            console.error('二维码生成错误:', error);
            this.drawErrorPlaceholder(ctx, size);
        }

        return canvas;
    },

    addBorderRadius: function(canvas, radius) {
        var ctx = canvas.getContext('2d');
        var size = canvas.width;

        ctx.globalCompositeOperation = 'destination-in';
        ctx.beginPath();
        ctx.roundRect(0, 0, size, size, radius);
        ctx.fill();
        ctx.globalCompositeOperation = 'source-over';
    },

    drawErrorPlaceholder: function(ctx, size) {
        // 绘制错误占位符
        ctx.fillStyle = '#f5f5f5';
        ctx.fillRect(0, 0, size, size);

        ctx.fillStyle = '#d9d9d9';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('二维码生成失败', size / 2, size / 2);
    }
};

// 全局变量
var customMsgTimer = null;
var intDiff = 0;

// 消息提示模块
var MessageModule = {
    show: function(content, timeout, type) {
        type = type || 'success';
        content = content.replace(/\n/g, '<br>');

        var msgLayer = Elements.getById("customMsgLayer");
        if (!msgLayer) {
            msgLayer = this.createMessageLayer();
        }

        this.updateMessageContent(msgLayer, content, type);
        this.positionMessage(msgLayer);
        this.showMessage(msgLayer, timeout);
    },

    createMessageLayer: function() {
        var msgLayer = document.createElement('div');
        msgLayer.id = 'customMsgLayer';
        msgLayer.className = 'message-layer';
        document.body.appendChild(msgLayer);
        Elements.cache['#customMsgLayer'] = msgLayer;
        return msgLayer;
    },

    updateMessageContent: function(msgLayer, content, type) {
        var iconMap = {
            success: {
                color: CONFIG.COLORS.SUCCESS,
                path: 'M768 320L448 640l-192-192'
            },
            error: {
                color: CONFIG.COLORS.ERROR,
                path: 'M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 0 1-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z'
            },
            warning: {
                color: CONFIG.COLORS.WARNING,
                path: 'M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z'
            }
        };

        var icon = iconMap[type] || iconMap.success;

        msgLayer.innerHTML =
            '<div class="custom-msg-content">' +
                '<span class="custom-msg-icon">' +
                    '<svg viewBox="0 0 1024 1024" width="20" height="20">' +
                        '<circle cx="512" cy="512" r="512" fill="' + icon.color + '"/>' +
                        '<path d="' + icon.path + '" stroke="#fff" stroke-width="60" stroke-linecap="round" stroke-linejoin="round" fill="none"/>' +
                    '</svg>' +
                '</span>' +
                '<span class="custom-msg-text">' + content + '</span>' +
            '</div>';
    },

    positionMessage: function(msgLayer) {
        var bodyEl = document.querySelector('.body');
        var bodyRect = bodyEl ? bodyEl.getBoundingClientRect() : { left: 0, width: 420 };
        var leftPosition = bodyRect.left + 20;

        DOMUtils.css(msgLayer, {
            position: 'fixed',
            top: '100px',
            left: leftPosition + 'px',
            background: '#fff',
            color: '#333',
            border: '1px solid #e9ecef',
            borderRadius: '12px',
            padding: '16px 20px',
            zIndex: '19891015',
            boxShadow: '0 12px 32px rgba(102, 126, 234, 0.15)',
            fontSize: '14px',
            minWidth: '200px',
            maxWidth: Math.min(bodyRect.width - 40, 320) + 'px',
            textAlign: 'left',
            opacity: '0',
            transform: 'translateY(-10px)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        });

        var contentEl = msgLayer.querySelector('.custom-msg-content');
        if (contentEl) {
            DOMUtils.css(contentEl, {
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px',
                lineHeight: '1.4'
            });
        }

        var textEl = msgLayer.querySelector('.custom-msg-text');
        if (textEl) {
            DOMUtils.css(textEl, {
                flex: '1',
                wordBreak: 'break-word'
            });
        }

        var iconEl = msgLayer.querySelector('.custom-msg-icon');
        if (iconEl) {
            DOMUtils.css(iconEl, {
                flexShrink: '0',
                marginTop: '1px'
            });
        }
    },

    showMessage: function(msgLayer, timeout) {
        // 显示消息
        setTimeout(function() {
            msgLayer.style.opacity = '1';
            msgLayer.style.transform = 'translateY(0)';
        }, 10);

        // 清除之前的定时器
        if (customMsgTimer) {
            clearTimeout(customMsgTimer);
        }

        // 设置自动隐藏
        customMsgTimer = setTimeout(function() {
            MessageModule.hide(msgLayer);
        }, timeout || CONFIG.MSG_TIMEOUT);
    },

    hide: function(msgLayer) {
        if (!msgLayer) return;

        msgLayer.style.opacity = '0';
        msgLayer.style.transform = 'translateY(-10px)';

        setTimeout(function() {
            msgLayer.style.display = 'none';
        }, 300);
    },

    success: function(content, timeout) {
        this.show(content, timeout, 'success');
    },

    error: function(content, timeout) {
        this.show(content, timeout, 'error');
    },

    warning: function(content, timeout) {
        this.show(content, timeout, 'warning');
    }
};

// 时间格式化工具
function formatDate(timestamp) {
    var date = new Date(timestamp);
    var pad = function(num) {
        return num > 9 ? num : "0" + num;
    };

    return date.getFullYear() + "-" +
           pad(date.getMonth() + 1) + "-" +
           pad(date.getDate()) + " " +
           pad(date.getHours()) + ":" +
           pad(date.getMinutes()) + ":" +
           pad(date.getSeconds());
}

// 时间工具
var TimeUtils = {
    formatDuration: function(seconds) {
        var hours = Math.floor(seconds / 3600);
        var minutes = Math.floor((seconds % 3600) / 60);
        var secs = Math.floor(seconds % 60);

        var parts = [];
        if (hours > 0) parts.push(hours + '时');
        if (minutes > 0) parts.push(minutes + '分');
        if (secs > 0 || parts.length === 0) parts.push(secs + '秒');

        return parts.join(' ');
    },

    pad: function(num) {
        return num <= 9 ? '0' + num : num;
    }
};

// 计时器模块
var TimerModule = {
    tickTimer: null,
    checkTimer: null,
    isWarning: false,

    start: function() {
        if (intDiff > 0) {
            this.clear();
            this.tickTimer = setInterval(function() {
                TimerModule.payTick();
                intDiff--;
            }, CONFIG.TICK_INTERVAL);

            this.checkTimer = setInterval(function() {
                PaymentModule.check();
            }, CONFIG.CHECK_INTERVAL);
        } else {
            PaymentModule.timeout('');
        }
    },

    clear: function() {
        if (this.tickTimer) {
            clearInterval(this.tickTimer);
            this.tickTimer = null;
        }
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
        this.isWarning = false;
    },

    payTick: function() {
        var totalSeconds = Math.max(0, intDiff);
        var hours = Math.floor(totalSeconds / 3600);
        var minutes = Math.floor((totalSeconds % 3600) / 60);
        var seconds = Math.floor(totalSeconds % 60);

        var pad = TimeUtils.pad;
        var hourShow = $('#hour_show');
        var minuteShow = $('#minute_show');
        var secondShow = $('#second_show');

        // 更新显示
        if (hours > 0) {
            DOMUtils.html(hourShow, hours + '时');
            DOMUtils.show(hourShow);
        } else {
            DOMUtils.hide(hourShow);
        }

        DOMUtils.html(minuteShow, pad(minutes) + '分');
        DOMUtils.html(secondShow, pad(seconds) + '秒');

        // 时间警告效果
        this.updateWarningState(totalSeconds);

        // 时间到了
        if (totalSeconds <= 0) {
            PaymentModule.timeout('');
            this.clear();
        }
    },

    updateWarningState: function(totalSeconds) {
        var countdownSection = $('.countdown-section');
        var timeItems = document.querySelectorAll('.time-item strong');

        // 少于5分钟时显示警告
        if (totalSeconds <= 300 && totalSeconds > 0) {
            if (!this.isWarning) {
                this.isWarning = true;
                if (countdownSection) {
                    DOMUtils.addClass(countdownSection, 'warning');
                }

                // 添加闪烁效果
                timeItems.forEach(function(item) {
                    DOMUtils.addClass(item, 'warning-blink');
                });

                MessageModule.warning('支付时间不足5分钟，请尽快完成支付！', 5000);
            }
        } else if (this.isWarning && totalSeconds > 300) {
            this.isWarning = false;
            if (countdownSection) {
                DOMUtils.removeClass(countdownSection, 'warning');
            }

            timeItems.forEach(function(item) {
                DOMUtils.removeClass(item, 'warning-blink');
            });
        }
    }
};

// 超时处理函数
function qrcode_timeout(msg) {
    var timeOutTip = $('#timeOutTip');
    var modTitle = $('.mod-title');
    var orderBody = $('#orderbody');
    var timeOut = $('#timeOut');

    if (msg == null || msg == '') {
        DOMUtils.show(timeOutTip);
    }

    DOMUtils.hide(modTitle);
    DOMUtils.hide(orderBody);

    var messageSpan = $('#timeOut .timeout-message');
    if (messageSpan) {
        DOMUtils.html(messageSpan, msg);
    }

    if (timeOut) {
        DOMUtils.css(timeOut, 'color', CONFIG.COLORS.ERROR);
    }

    // 显示超时区域
    DOMUtils.show(timeOut);
}

// URL参数获取工具
function getQueryString(name) {
    var regex = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var result = regex.exec(window.location.search.substr(1));
    return result != null ? decodeURI(result[2]) : null;
}

// 支付模块
var PaymentModule = {
    nowPayType: -1,
    wxUrl: "",
    zfbUrl: "",
    otherUrl: "",
    orderFrom: 0,
    isLoading: false,

    // 切换支付方式
    switchPayType: function(payType) {
        if (this.isLoading) {
            MessageModule.warning('正在处理中，请稍候...', 1500);
            return;
        }

        var url = window.location.href;
        var isAlipay = payType == 0 || payType == 2;

        // 确定支付URL
        if (this.orderFrom != 0) {
            url = this.otherUrl;
        } else {
            url = isAlipay ? this.zfbUrl : this.wxUrl;
        }

        if (url == null || url == "") {
            url = window.location.href;
        }

        this.updatePaymentUI(payType, isAlipay);
        this.updateQRCode(url);
        this.changePayType(payType);
    },

    // 更新支付界面
    updatePaymentUI: function(payType, isAlipay) {
        var alipayLi = $('#alipayLi');
        var wxpayLi = $('#wxpayLi');
        var payTypeMsg = $('#payTypeMsg');

        // 移除所有选中状态
        DOMUtils.removeClass(alipayLi, 'pay_clickli');
        DOMUtils.removeClass(wxpayLi, 'pay_clickli');

        // 添加选中状态和更新文字
        if (isAlipay) {
            DOMUtils.addClass(alipayLi, 'pay_clickli');
            DOMUtils.text(payTypeMsg, '支付宝');
        } else {
            DOMUtils.addClass(wxpayLi, 'pay_clickli');
            DOMUtils.text(payTypeMsg, '微信支付');
        }

        // 添加切换动画效果
        this.addSwitchAnimation(isAlipay ? alipayLi : wxpayLi);
    },

    // 添加切换动画
    addSwitchAnimation: function(element) {
        if (!element) return;

        element.style.transform = 'scale(0.95)';
        element.style.transition = 'transform 0.2s ease';

        setTimeout(function() {
            element.style.transform = 'scale(1)';
        }, 100);
    },

    // 更新二维码
    updateQRCode: function(url) {
        var qrcodeEl = $('#qrcode');
        if (!qrcodeEl) return;

        // 显示加载状态
        this.showQRCodeLoading(qrcodeEl);

        setTimeout(function() {
            DOMUtils.html(qrcodeEl, "");

            if (url.indexOf("/static") == 0) {
                // 静态图片
                var img = document.createElement('img');
                img.src = url;
                img.alt = '支付二维码';
                img.style.maxWidth = '100%';
                img.style.height = 'auto';

                img.onload = function() {
                    PaymentModule.hideQRCodeLoading();
                };

                img.onerror = function() {
                    PaymentModule.showQRCodeError(qrcodeEl);
                };

                qrcodeEl.appendChild(img);
            } else {
                // 生成二维码
                try {
                    var canvas = QRCode.generate(url, {
                        width: CONFIG.QRCODE_SIZE,
                        height: CONFIG.QRCODE_SIZE,
                        foreground: "#000000",
                        background: "#ffffff",
                        borderRadius: 8
                    });

                    qrcodeEl.appendChild(canvas);
                    PaymentModule.hideQRCodeLoading();
                } catch (error) {
                    console.error('二维码生成失败:', error);
                    PaymentModule.showQRCodeError(qrcodeEl);
                }
            }
        }, 300);
    },

    // 显示二维码加载状态
    showQRCodeLoading: function(container) {
        var loadingEl = document.createElement('div');
        loadingEl.className = 'qrcode-loading';
        loadingEl.innerHTML =
            '<div class="loading-spinner"></div>' +
            '<div class="loading-text">生成二维码中...</div>';

        DOMUtils.css(loadingEl, {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: CONFIG.QRCODE_SIZE + 'px',
            color: '#999'
        });

        container.appendChild(loadingEl);
    },

    // 隐藏二维码加载状态
    hideQRCodeLoading: function() {
        var loadingEl = $('.qrcode-loading');
        if (loadingEl) {
            loadingEl.remove();
        }
    },

    // 显示二维码错误
    showQRCodeError: function(container) {
        this.hideQRCodeLoading();

        var errorEl = document.createElement('div');
        errorEl.className = 'qrcode-error';
        errorEl.innerHTML =
            '<div class="error-icon">⚠️</div>' +
            '<div class="error-text">二维码生成失败</div>' +
            '<button class="retry-btn" onclick="PaymentModule.retryQRCode()">重试</button>';

        DOMUtils.css(errorEl, {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: CONFIG.QRCODE_SIZE + 'px',
            color: '#999',
            textAlign: 'center'
        });

        container.appendChild(errorEl);
    },

    // 重试生成二维码
    retryQRCode: function() {
        var url = this.getCurrentPayUrl();
        this.updateQRCode(url);
    },

    // 获取当前支付URL
    getCurrentPayUrl: function() {
        var isAlipay = this.nowPayType == 0 || this.nowPayType == 2;

        if (this.orderFrom != 0) {
            return this.otherUrl;
        }

        return isAlipay ? this.zfbUrl : this.wxUrl;
    },

    // 改变支付类型
    changePayType: function(payType) {
        if (this.nowPayType == payType) {
            var payTypeName = (payType == 0 ? "支付宝" : "微信支付");
            MessageModule.success(
                "请使用【" + payTypeName + "】扫码支付！<br>" +
                "也可以在顶部切换其他付款方式！",
                2000
            );
            return;
        }

        this.isLoading = true;
        this.nowPayType = payType;

        var orderId = getQueryString("orderId");
        var self = this;

        AjaxUtils.post(
            "/code.aspx?op=changePayType",
            "orderId=" + orderId + "&payType=" + payType,
            function(response) {
                self.isLoading = false;
                self.handlePayTypeChangeSuccess(payType);
            },
            function(error) {
                self.isLoading = false;
                self.handlePayTypeChangeError(payType, error);
            }
        );
    },

    // 处理支付类型切换成功
    handlePayTypeChangeSuccess: function(payType) {
        var payTypeName = (payType == 0 ? "支付宝" : "微信支付");

        if (navigator.userAgent.match(/MicroMessenger\//i) && payType == 1) {
            MessageModule.success("支付方式变更为【微信支付】，请重新扫码！", 3000);
        } else {
            MessageModule.success(
                "请使用【" + payTypeName + "】扫码支付！<br>" +
                "也可以在顶部切换其他付款方式！",
                2000
            );
        }
    },

    // 处理支付类型切换错误
    handlePayTypeChangeError: function(payType, error) {
        MessageModule.error("切换支付方式失败，请重试", 3000);
        console.error('支付类型切换失败:', error);
    },

    // 超时处理
    timeout: function(msg) {
        qrcode_timeout(msg);
    }
};

// 全局函数
function changePay(payType) {
    PaymentModule.switchPayType(payType);
}

function showDetail() {
    var orderDetail = $('#orderDetail');
    var detailContent = $('#orderDetail .detail-ct');
    var arrow = orderDetail.querySelector('.ico-arrow');

    if (DOMUtils.hasClass(orderDetail, 'detail-open')) {
        DOMUtils.fadeOut(detailContent, 200);
        DOMUtils.removeClass(orderDetail, 'detail-open');
        if (arrow) {
            arrow.style.transform = 'rotate(0deg)';
        }
    } else {
        DOMUtils.fadeIn(detailContent, 200);
        DOMUtils.addClass(orderDetail, 'detail-open');
        if (arrow) {
            arrow.style.transform = 'rotate(180deg)';
        }
    }
}

// 扩展支付模块功能
PaymentModule.initOrder = function() {
    var orderId = getQueryString("orderId");
    if (!orderId) {
        this.handleOrderError("订单ID不存在");
        return;
    }

    var self = this;
    AjaxUtils.post(
        "/code.aspx?op=getOrder",
        "orderId=" + orderId,
        function(data) {
            if (data.code == 1) {
                self.handleOrderData(data.data);
            } else {
                self.handleOrderError(data.msg || "获取订单信息失败");
            }
        },
        function(error) {
            self.handleOrderError("网络错误，请检查网络连接");
        }
    );
};

PaymentModule.handleOrderData = function(orderData) {
    var timeLeft = 0;

    // 检查订单状态
    if (orderData.state >= 1) {
        this.handlePaymentSuccess(orderData);
        return;
    }

    // 计算剩余时间
    if (orderData.state != -1) {
        var currentTime = new Date().getTime();
        var orderTime = orderData.date;
        var elapsed = (currentTime - orderTime) / 1000;
        timeLeft = Math.max(0, orderData.timeOut * 60 - elapsed);
    }

    // 设置支付URL
    this.zfbUrl = orderData.zfbPayUrl;
    this.wxUrl = orderData.wxPayUrl;
    this.otherUrl = orderData.payUrl;
    this.orderFrom = orderData.from;

    // 处理特殊浏览器环境
    if (this.handleSpecialBrowser(orderData)) {
        return;
    }

    // 更新界面
    this.updateOrderInfo(orderData);
    this.showOrderInterface();

    // 启动支付流程
    if (timeLeft > 0) {
        DOMUtils.css($('.mod-title'), 'display', 'flex');
        this.switchPayType(orderData.payType);
    }

    window.intDiff = timeLeft;
    TimerModule.start();
    this.check();

    // 设置客服
    if (orderData.qq != null) {
        this.setupCustomerService(orderData.qq);
    }
};

PaymentModule.handlePaymentSuccess = function(orderData) {
    this.timeout("恭喜，订单支付成功!");

    if (orderData.returnUrl != null && orderData.returnUrl != '') {
        MessageModule.success("支付成功，正在跳转...", 2000);
        setTimeout(function() {
            window.location.href = orderData.returnUrl;
        }, 2000);
    }
};

PaymentModule.handleSpecialBrowser = function(orderData) {
    // 支付宝浏览器环境
    if (navigator.userAgent.match(/Alipay/i) &&
        this.zfbUrl != null && this.zfbUrl != '') {
        window.location.href = this.zfbUrl;
        return true;
    }

    // 微信浏览器环境
    if (navigator.userAgent.match(/MicroMessenger\//i) &&
        this.wxUrl != null && this.wxUrl != '') {
        if (orderData.payType != 1) {
            orderData.payType = 1;
            this.changePayType(orderData.payType);
            return true;
        }
    }

    return false;
};

PaymentModule.updateOrderInfo = function(orderData) {
    var updates = {
        'strRemark': orderData.remark,
        'money': "￥" + orderData.reallyPrice.toFixed(2),
        'strPrice': orderData.price,
        'strPayId': orderData.payId,
        'strOrderId': orderData.orderId,
        'strDate': formatDate(orderData.date)
    };

    Object.keys(updates).forEach(function(id) {
        var element = Elements.getById(id);
        if (element) {
            if (id === 'strRemark') {
                // 更新订单标题
                DOMUtils.text(element, updates[id]);
            } else if (id === 'money') {
                // 更新金额显示
                DOMUtils.text(element, updates[id]);
            } else {
                DOMUtils.text(element, updates[id]);
            }
        }
    });

    this.nowPayType = orderData.payType;

    // 显示优惠提示
    if (orderData.needUserPay) {
        DOMUtils.show($('#lblYouHui'));
    }
};

PaymentModule.showOrderInterface = function() {
    var orderDiv = $('#orderDiv');
    var loadingDiv = $('#loadingDiv');

    DOMUtils.fadeOut(loadingDiv, 200, function() {
        DOMUtils.fadeIn(orderDiv, 300);
    });
};

PaymentModule.setupCustomerService = function(qq) {
    var qqHead = $('#qqHead');
    var keFuQQ = $('#keFuQQ');
    var keFuQQ1 = $('#keFuQQ1');

    if (qqHead) {
        DOMUtils.attr(qqHead, 'src', "https://q1.qlogo.cn/g?b=qq&nk=" + qq + "&s=100");
        DOMUtils.show(qqHead);
    }

    var qqUrl = "http://wpa.qq.com/msgrd?v=3&uin=" + qq + "&site=qq&menu=yes";

    if (keFuQQ) {
        DOMUtils.attr(keFuQQ, 'href', qqUrl);
    }

    if (keFuQQ1) {
        DOMUtils.attr(keFuQQ1, 'href', qqUrl);
    }
};

PaymentModule.handleOrderError = function(errorMsg) {
    this.showOrderInterface();
    this.timeout(errorMsg);
    MessageModule.error(errorMsg, 5000);
};

PaymentModule.check = function() {
    var orderId = getQueryString("orderId");
    if (!orderId) return;

    var self = this;
    AjaxUtils.post(
        "/code.aspx?op=checkOrder",
        "orderId=" + orderId,
        function(data) {
            if (data.code == 1) {
                // 支付成功，跳转
                MessageModule.success("支付成功，正在跳转...", 2000);
                setTimeout(function() {
                    window.location.href = data.data;
                }, 1000);
            } else {
                // 更新剩余时间
                if (data.date == null || data.date < 0) {
                    data.date = 0;
                }

                window.intDiff = data.date;
                if (window.intDiff <= 0) {
                    window.intDiff = 0;
                }

                // 检查支付方式是否变更
                if (window.intDiff != 0 &&
                    data.payType != null &&
                    self.nowPayType != data.payType) {
                    self.switchPayType(data.payType);
                }
            }
        },
        function(error) {
            console.error('订单检查失败:', error);
        }
    );
};

// 初始化订单
PaymentModule.initOrder();
    </script>
</body>
</html>
